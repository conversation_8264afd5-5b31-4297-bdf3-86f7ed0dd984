#!/usr/bin/env python3
"""
单元测试：验证数据类型转换函数
直接测试修复后的数据类型转换逻辑
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_data_type_conversion():
    """
    测试数据类型转换逻辑
    """
    print("=" * 60)
    print("单元测试：数据类型转换")
    print("=" * 60)
    
    # 模拟原始数据（float类型）
    test_cases = [
        {"rate_final": 90.57, "rate_recall": 22.64, "rate_category": 36.22},
        {"rate_final": 85.0, "rate_recall": 30.0, "rate_category": 44.44},
        {"rate_final": 92.33, "rate_recall": 18.75, "rate_category": 31.11},
        {"rate_final": 0.0, "rate_recall": 0.0, "rate_category": 0.0},
        {"rate_final": 100.0, "rate_recall": 100.0, "rate_category": 100.0},
    ]
    
    print("测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"  案例 {i}: rate_final={case['rate_final']}, rate_recall={case['rate_recall']}, rate_category={case['rate_category']}")
    print()
    
    all_tests_passed = True
    
    for i, target_data in enumerate(test_cases, 1):
        print(f"测试案例 {i}:")
        print(f"  原始数据: {target_data}")
        
        # 应用修复后的安全转换逻辑
        def safe_int_convert(value):
            """安全地将值转换为整数，处理None、空字符串等边界情况"""
            try:
                if value is None or value == "":
                    return 0
                return int(round(float(value)))
            except (ValueError, TypeError):
                return 0

        accuracy = safe_int_convert(target_data.get("rate_final", 0))
        recall = safe_int_convert(target_data.get("rate_recall", 0))
        f1_score = safe_int_convert(target_data.get("rate_category", 0))
        
        print(f"  转换后:")
        print(f"    accuracy: {accuracy} (类型: {type(accuracy).__name__})")
        print(f"    recall: {recall} (类型: {type(recall).__name__})")
        print(f"    f1_score: {f1_score} (类型: {type(f1_score).__name__})")
        
        # 验证类型
        type_check_passed = True
        if not isinstance(accuracy, int):
            print(f"    ❌ accuracy 类型错误: 期望 int, 实际 {type(accuracy).__name__}")
            type_check_passed = False
            all_tests_passed = False
        
        if not isinstance(recall, int):
            print(f"    ❌ recall 类型错误: 期望 int, 实际 {type(recall).__name__}")
            type_check_passed = False
            all_tests_passed = False
            
        if not isinstance(f1_score, int):
            print(f"    ❌ f1_score 类型错误: 期望 int, 实际 {type(f1_score).__name__}")
            type_check_passed = False
            all_tests_passed = False
        
        if type_check_passed:
            print(f"    ✅ 所有类型检查通过")
        
        # 验证数值合理性
        expected_accuracy = int(round(target_data["rate_final"]))
        expected_recall = int(round(target_data["rate_recall"]))
        expected_f1_score = int(round(target_data["rate_category"]))
        
        if accuracy == expected_accuracy and recall == expected_recall and f1_score == expected_f1_score:
            print(f"    ✅ 数值转换正确")
        else:
            print(f"    ❌ 数值转换错误")
            print(f"       期望: accuracy={expected_accuracy}, recall={expected_recall}, f1_score={expected_f1_score}")
            print(f"       实际: accuracy={accuracy}, recall={recall}, f1_score={f1_score}")
            all_tests_passed = False
        
        print()
    
    # 总结
    if all_tests_passed:
        print("🎉 所有单元测试通过！数据类型转换修复成功。")
    else:
        print("❌ 存在测试失败")
    
    return all_tests_passed


def test_edge_cases():
    """
    测试边界情况
    """
    print("=" * 60)
    print("边界情况测试")
    print("=" * 60)
    
    edge_cases = [
        {"rate_final": None, "rate_recall": None, "rate_category": None},
        {"rate_final": "", "rate_recall": "", "rate_category": ""},
        {"rate_final": "90.57", "rate_recall": "22.64", "rate_category": "36.22"},  # 字符串类型
        {},  # 空字典
    ]
    
    all_edge_tests_passed = True
    
    for i, target_data in enumerate(edge_cases, 1):
        print(f"边界测试案例 {i}: {target_data}")
        
        try:
            # 应用修复后的安全转换逻辑
            def safe_int_convert(value):
                """安全地将值转换为整数，处理None、空字符串等边界情况"""
                try:
                    if value is None or value == "":
                        return 0
                    return int(round(float(value)))
                except (ValueError, TypeError):
                    return 0

            accuracy = safe_int_convert(target_data.get("rate_final", 0))
            recall = safe_int_convert(target_data.get("rate_recall", 0))
            f1_score = safe_int_convert(target_data.get("rate_category", 0))
            
            print(f"  结果: accuracy={accuracy}, recall={recall}, f1_score={f1_score}")
            print(f"  ✅ 边界情况处理正常")
            
        except Exception as e:
            print(f"  ❌ 边界情况处理失败: {e}")
            all_edge_tests_passed = False
        
        print()
    
    return all_edge_tests_passed


if __name__ == "__main__":
    print("开始数据类型转换测试...\n")
    
    test1_passed = test_data_type_conversion()
    test2_passed = test_edge_cases()
    
    overall_success = test1_passed and test2_passed
    
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    if overall_success:
        print("🎉 所有测试通过！修复成功。")
        print("\n修复内容:")
        print("1. 将 accuracy 字段从 float 转换为 int")
        print("2. 将 recall 字段从 float 转换为 int") 
        print("3. 将 f1_score 字段从 float 转换为 int")
        print("\n现在接口返回的数据类型符合接口定义要求。")
    else:
        print("❌ 存在测试失败，需要进一步检查。")
    
    sys.exit(0 if overall_success else 1)
