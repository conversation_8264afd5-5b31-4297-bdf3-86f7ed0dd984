# 维度指标折线图数据修复说明

## 问题描述

在测试获取某个分类（各模块\维度）在多个结果中的准确率、召回率、F1分数对比折线图数据接口时，发现输入多个task_id时获取的数据数量与实际数量不符。

**接口路径**: `/test-report/category/multi-result-group`

**问题现象**: 
- 输入3个task_id，期望返回3条数据
- 实际返回了多于3条的数据（可能是9条、12条等）

## 问题分析

### 根本原因

1. **数据重复问题**: 在 `get_metrics_by_group_name_under_task_id` 函数中，对于每个 `task_id`，会查询该任务下的**所有** `task_result`（最多100个），然后对每个 `task_result` 都会返回一条匹配的数据。

2. **预期行为 vs 实际行为**:
   - **预期**: 每个 `task_id` 应该只返回一条最新的数据
   - **实际**: 每个 `task_id` 返回了该任务下所有 `task_result` 的数据

### 问题代码位置

**文件**: `com/exturing/ai/test/service/test_report_service.py`

**函数**: `get_metrics_by_group_name_under_task_id` (第718行)

**问题代码**:
```python
# 原始代码 - 获取所有task_result
all_task_results = TestTaskResult.find_condition(cond, [("create_time", -1)], 100, 0)

# 然后对每个task_result都处理
for tr in all_task_results:
    # ... 处理逻辑
```

### 附加问题

在 `_group_statistics` 函数中还发现了一个变量引用错误：

**问题代码**:
```python
# 第430行 - 错误地使用了 ri 而不是 di
raw_dim = ri.get("dimension_id")  # 应该是 di.get("dimension_id")
```

## 修复方案

### 主要修复

1. **限制查询结果**: 将查询所有 `task_result` 改为只查询最新的一个
2. **避免重复处理**: 找到匹配的分组后立即跳出循环

**修复后的代码**:
```python
# 修复：只获取最新的一个 task_result，而不是所有的
latest_task_results = TestTaskResult.find_condition(cond, [("create_time", -1)], 1, 0)

# 只处理最新的一个 task_result
tr = latest_task_results[0]
# ... 处理逻辑

# 修复：找到匹配项后立即跳出循环，避免重复添加
if current_group == group_name.strip():
    matched = True
    result_list.append({...})
    break  # 立即跳出循环
```

### 附加修复

修复变量引用错误：
```python
# 修复前
raw_dim = ri.get("dimension_id")

# 修复后
raw_dim = di.get("dimension_id")  # 使用正确的变量
```

## 修复效果

### 修复前
- 输入3个task_id，可能返回9-12条数据
- 每个task_id对应多条记录（该任务的所有历史结果）

### 修复后
- 输入3个task_id，返回3条数据
- 每个task_id对应1条记录（该任务的最新结果）

## 测试验证

### 测试脚本
已创建测试脚本 `test_dimension_metrics_fix.py` 用于验证修复效果。

### 测试步骤
1. 准备测试数据（真实的task_id）
2. 运行测试脚本
3. 检查返回数据数量是否正确
4. 验证每个task_id只返回一条最新记录

### 验证要点
- ✅ 返回数据数量 = 输入task_id数量
- ✅ 无重复的task_id
- ✅ 每条记录都是对应task_id的最新结果
- ✅ 数据内容准确（准确率、召回率、F1分数）

## 影响范围

### 直接影响
- **接口**: `/test-report/category/multi-result-group`
- **功能**: 维度指标折线图数据获取

### 间接影响
- 提高了查询性能（只查询最新结果而不是所有历史结果）
- 修复了数据集统计中的变量引用错误

## 注意事项

1. **向后兼容**: 修复不会影响现有的API接口结构
2. **性能提升**: 查询效率得到显著提升
3. **数据一致性**: 确保返回的数据逻辑正确

## 建议

1. **测试验证**: 在生产环境部署前，建议使用真实数据进行充分测试
2. **监控观察**: 部署后观察相关接口的性能和数据准确性
3. **文档更新**: 如有必要，更新相关的API文档说明

---

**修复时间**: 2025-07-29  
**修复文件**: `com/exturing/ai/test/service/test_report_service.py`  
**修复函数**: `get_metrics_by_group_name_under_task_id`, `_group_statistics`
