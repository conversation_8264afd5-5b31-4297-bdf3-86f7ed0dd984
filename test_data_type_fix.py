#!/usr/bin/env python3
"""
测试脚本：验证接口返回数据类型修复
用于测试 accuracy、recall、f1_score 字段是否为整数类型
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from com.exturing.ai.test.service.test_report_service import get_dimension_metrics_line_chart_data
    from com.exturing.ai.test.comm.log_tool import et_log
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的项目环境中运行此脚本")
    sys.exit(1)


def test_data_types():
    """
    测试返回数据的类型是否正确
    """
    print("=" * 60)
    print("测试：接口返回数据类型修复")
    print("=" * 60)
    
    # 测试参数 - 使用您提供的实际参数
    test_task_ids = ["6887944b7293f6f5143f0a79", "67d39611ae0602220c7b5ced"]
    test_group_name = "无维度"
    test_group_by = "dimension_id"
    
    print(f"测试参数:")
    print(f"  task_ids: {test_task_ids}")
    print(f"  group_name: {test_group_name}")
    print(f"  group_by: {test_group_by}")
    print()
    
    try:
        # 调用修复后的函数
        result = get_dimension_metrics_line_chart_data(
            task_ids=test_task_ids,
            group_by=test_group_by,
            group_name=test_group_name
        )
        
        print(f"返回结果数量: {len(result)}")
        print()
        
        # 检查数据类型
        all_types_correct = True
        for i, item in enumerate(result):
            print(f"数据项 {i+1}:")
            print(f"  series_name: {item.get('series_name', 'N/A')}")
            print(f"  task_id: {item.get('task_id', 'N/A')}")
            print(f"  group_name: {item.get('group_name', 'N/A')}")
            
            # 检查关键字段的数据类型
            accuracy = item.get('accuracy')
            recall = item.get('recall')
            f1_score = item.get('f1_score')
            
            print(f"  accuracy: {accuracy} (类型: {type(accuracy).__name__})")
            print(f"  recall: {recall} (类型: {type(recall).__name__})")
            print(f"  f1_score: {f1_score} (类型: {type(f1_score).__name__})")
            
            # 验证类型
            if not isinstance(accuracy, int):
                print(f"  ❌ accuracy 应该是 int 类型，实际是 {type(accuracy).__name__}")
                all_types_correct = False
            else:
                print(f"  ✅ accuracy 类型正确")
                
            if not isinstance(recall, int):
                print(f"  ❌ recall 应该是 int 类型，实际是 {type(recall).__name__}")
                all_types_correct = False
            else:
                print(f"  ✅ recall 类型正确")
                
            if not isinstance(f1_score, int):
                print(f"  ❌ f1_score 应该是 int 类型，实际是 {type(f1_score).__name__}")
                all_types_correct = False
            else:
                print(f"  ✅ f1_score 类型正确")
            
            print()
        
        # 总结
        if all_types_correct:
            print("🎉 所有数据类型检查通过！")
        else:
            print("❌ 存在数据类型错误")
            
        # 输出JSON格式用于对比
        print("\n" + "="*60)
        print("JSON格式输出（用于对比）:")
        print("="*60)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return all_types_correct
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_data_types()
    sys.exit(0 if success else 1)
