# 接口返回数据类型修复说明

## 问题描述

在测试获取某个分类（各模块\维度）在各个结果中的准确率、召回率、F1分数对比折线图数据接口时，发现返回数据结构与接口定义不一致：

**接口路径**: `/test-report/category/multi-result-group`

**请求体示例**:
```json
{
  "task_ids": ["6887944b7293f6f5143f0a79","67d39611ae0602220c7b5ced"],
  "group_name":"无维度"
}
```

**实际返回**:
```json
{
  "code": 200,
  "data": [
    {
      "accuracy": 90.57,
      "f1_score": 36.22,
      "group_name": "无维度",
      "recall": 22.64,
      "report_time": "2025-07-29 00:27:50",
      "series_name": "任务_6887944b7293f6f5143f0a79(2025-07-29)",
      "task_id": "6887944b7293f6f5143f0a79",
      "task_result_id": "6887a506e28c89fb1cb64df6"
    }
  ],
  "msg": "success"
}
```

**错误信息**:
1. `$.data[0].accuracy` 应当是 `integer` 类型，实际返回 `float`
2. `$.data[0].f1_score` 应当是 `integer` 类型，实际返回 `float`
3. `$.data[0].recall` 应当是 `integer` 类型，实际返回 `float`

## 问题分析

### 根本原因

数据类型不一致的问题出现在数据处理和返回的过程中：

1. **计算过程**: `_calculate_metrics()` 函数使用 `round()` 函数计算准确率、召回率和F1分数，返回的是 `float` 类型
2. **数据流转**: 这些 `float` 值被存储在 `chart_data` 中，然后直接用于构造最终返回数据
3. **接口定义**: 接口定义要求这些字段应该是 `integer` 类型

### 问题代码位置

**文件**: `com/exturing/ai/test/service/test_report_service.py`

**涉及函数**:
- `_get_dimension_metrics_from_chart_data()` (第943行)
- `_get_category_metrics_from_summary()` (第891行)

## 修复方案

### 修复内容

1. **添加安全类型转换函数**:
   ```python
   def safe_int_convert(value):
       """安全地将值转换为整数，处理None、空字符串等边界情况"""
       try:
           if value is None or value == "":
               return 0
           return int(round(float(value)))
       except (ValueError, TypeError):
           return 0
   ```

2. **修复 `_get_dimension_metrics_from_chart_data` 函数**:
   ```python
   return {
       "series_name": series_name,
       "task_result_id": actual_task_result_id,
       "task_id": actual_task_id,
       "group_name": group_name,
       "accuracy": safe_int_convert(target_data.get("rate_final", 0)),      # 安全转换为整数类型
       "recall": safe_int_convert(target_data.get("rate_recall", 0)),       # 安全转换为整数类型
       "f1_score": safe_int_convert(target_data.get("rate_category", 0)),   # 安全转换为整数类型
       "report_time": tr.get("create_time", ""),
   }
   ```

3. **修复 `_get_category_metrics_from_summary` 函数**:
   ```python
   accuracy = safe_int_convert(tr.get("rate_final", 0))
   recall   = safe_int_convert(tr.get("rate_recall", 0))
   f1 = safe_int_convert(tr.get("rate_category", 0))
   ```

### 修复特点

1. **类型安全**: 使用 `safe_int_convert` 函数确保类型转换的安全性
2. **边界处理**: 正确处理 `None`、空字符串、无效数值等边界情况
3. **数值保持**: 通过 `round()` 函数保持数值的合理性（四舍五入到最近整数）
4. **向后兼容**: 不影响现有的计算逻辑，只改变最终输出的数据类型

## 修复后效果

**修复后返回**:
```json
{
  "code": 200,
  "data": [
    {
      "accuracy": 91,        // 现在是整数类型
      "f1_score": 36,        // 现在是整数类型
      "group_name": "无维度",
      "recall": 23,          // 现在是整数类型
      "report_time": "2025-07-29 00:27:50",
      "series_name": "任务_6887944b7293f6f5143f0a79(2025-07-29)",
      "task_id": "6887944b7293f6f5143f0a79",
      "task_result_id": "6887a506e28c89fb1cb64df6"
    }
  ],
  "msg": "success"
}
```

## 测试验证

创建了单元测试 `test_data_type_unit.py` 验证修复效果：

1. **正常情况测试**: 验证 float 到 int 的正确转换
2. **边界情况测试**: 验证 None、空字符串、字符串数字等边界情况的处理
3. **类型检查**: 确保返回的 `accuracy`、`recall`、`f1_score` 都是 `int` 类型

测试结果：✅ 所有测试通过

## 影响范围

此修复仅影响以下接口的返回数据类型：
- `/test-report/category/multi-result-group` (某个分类在各个结果中的指标对比折线图)
- 其他调用相同函数的接口

修复不会影响：
- 数据计算逻辑
- 数据库存储
- 其他接口的功能

## 总结

通过添加安全的类型转换函数，成功解决了接口返回数据类型与定义不一致的问题。修复后的接口现在返回符合定义要求的整数类型数据，同时保持了数值的准确性和系统的稳定性。
