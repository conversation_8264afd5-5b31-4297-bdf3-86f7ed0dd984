#!/usr/bin/env python3
"""
测试脚本：验证维度指标折线图数据修复
用于测试多个task_id时获取的数据数量是否正确
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from com.exturing.ai.test.service.test_report_service import get_dimension_metrics_line_chart_data
    from com.exturing.ai.test.comm.log_tool import et_log
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的项目环境中运行此脚本")
    sys.exit(1)


def test_dimension_metrics_fix():
    """
    测试维度指标折线图数据修复
    """
    print("=" * 60)
    print("测试：多个task_id的维度指标折线图数据")
    print("=" * 60)
    
    # 测试参数 - 请根据实际情况修改这些ID
    test_task_ids = [
        "6740123456789abcdef01234",  # 替换为实际的task_id
        "6740123456789abcdef01235",  # 替换为实际的task_id
        "6740123456789abcdef01236",  # 替换为实际的task_id
    ]
    
    test_group_by = "dimension_id"
    test_group_name = "测试维度"  # 替换为实际的维度名称
    
    print(f"测试参数:")
    print(f"  task_ids: {test_task_ids}")
    print(f"  group_by: {test_group_by}")
    print(f"  group_name: {test_group_name}")
    print()
    
    try:
        # 调用修复后的函数
        result = get_dimension_metrics_line_chart_data(
            task_ids=test_task_ids,
            group_by=test_group_by,
            group_name=test_group_name
        )
        
        print(f"返回结果数量: {len(result)}")
        print(f"预期结果数量: {len(test_task_ids)} (每个task_id一条记录)")
        
        if len(result) == len(test_task_ids):
            print("✅ 测试通过：返回数据数量正确")
        else:
            print("❌ 测试失败：返回数据数量不正确")
        
        print("\n详细结果:")
        for i, item in enumerate(result):
            print(f"  [{i+1}] task_id: {item.get('task_id')}")
            print(f"      task_result_id: {item.get('task_result_id')}")
            print(f"      series_name: {item.get('series_name')}")
            print(f"      group_name: {item.get('group_name')}")
            print(f"      rate_final: {item.get('rate_final')}")
            print(f"      rate_recall: {item.get('rate_recall')}")
            print(f"      rate_category: {item.get('rate_category')}")
            print(f"      report_time: {item.get('report_time')}")
            print()
        
        # 检查是否有重复的task_id
        task_ids_in_result = [item.get('task_id') for item in result]
        unique_task_ids = set(task_ids_in_result)
        
        if len(task_ids_in_result) == len(unique_task_ids):
            print("✅ 无重复task_id")
        else:
            print("❌ 存在重复task_id")
            print(f"   原始数量: {len(task_ids_in_result)}")
            print(f"   去重后数量: {len(unique_task_ids)}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_with_mock_data():
    """
    使用模拟数据进行测试（当没有真实数据时）
    """
    print("\n" + "=" * 60)
    print("模拟数据测试")
    print("=" * 60)
    
    # 这里可以添加模拟数据的测试逻辑
    print("提示：如果没有真实的task_id数据，可以在这里添加模拟测试")
    print("建议：")
    print("1. 在数据库中创建一些测试数据")
    print("2. 或者使用现有的真实task_id进行测试")
    print("3. 检查日志输出以确认修复是否生效")


if __name__ == "__main__":
    print("维度指标折线图数据修复测试")
    print(f"测试时间: {datetime.now()}")
    print()
    
    # 运行主要测试
    result = test_dimension_metrics_fix()
    
    # 运行模拟数据测试
    test_with_mock_data()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    if result is not None:
        print("建议：")
        print("1. 检查返回的数据数量是否符合预期")
        print("2. 确认每个task_id只返回一条最新记录")
        print("3. 验证数据的准确性和完整性")
    else:
        print("请检查：")
        print("1. 数据库连接是否正常")
        print("2. 测试的task_id是否存在")
        print("3. group_name是否正确")
